
-- Finding sales lines delivered, but not invoiced
-- Partially released inventory

WITH PartRel AS
(
SELECT 
    sl.SALESID
    , st.CustAccount
    , st.email
    , sl.ITEMID
    , idim.INVENTCOLORID        AS [Color]
    , idim.INVENTSIZEID         AS [Size]
    , sl.lineamount       
FROM
    salesline sl
    LEFT JOIN WHSLOADLINE ll ON sl.DATAAREAID = ll.DATAAREAID AND sl.[PARTITION] = ll.[PARTITION] AND sl.INVENTTRANSID = ll.INVENTTRANSID AND ll.INVENTTRANSTYPE = 0 -- Sales transaction types only
    LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = ll.INVENTDIMID AND idim.DATAAREAID = ll.DATAAREAID AND idim.[PARTITION] = ll.[PARTITION]
    INNER JOIN SALESTABLE st ON st.SALESID = sl.SALESID AND sl.DATAAREAID = st.DATAAREAID AND st.[PARTITION] = sl.[PARTITION]
WHERE
    sl.SALESSTATUS > 1 -- sale line delivered
    AND st.SALESSTATUS = 1 -- Open order
    AND st.ReleaseStatus = 1  -- Partially released
    AND st.SALESTYPE = 3 -- Sales order
    AND DATEADD(DD,-45, GETUTCDATE()) < sl.CREATEDDATETIME
    --AND sl.SALESID = '********'
) 
SELECT
    SalesID
    , CustAccount
    , email
    , CONVERT(DECIMAL(10,2), sum(LineAmount))   AS [AmountToCollect]
FROM
    PartRel
GROUP BY SalesID, CustAccount, email

--sp_columns salestable