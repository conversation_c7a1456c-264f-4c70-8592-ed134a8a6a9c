
-- Orders pending export from Voice after the voice integration (10/2024)
WITH PendingOrders AS (
    SELECT
        ISNULL(SUM(CASE WHEN wktbl.WORKTEMPLATECODE = '4010 Direct' THEN 1 ELSE 0 END), 0) AS PendingDirect,
        ISNULL(SUM(CASE WHEN wktbl.WORKTEMPLATECODE != '4010 Direct' THEN 1 ELSE 0 END), 0) AS PendingAB
    FROM HAVOICEINTEGRATIONQUEUEPUTS vputs
    JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = vputs.WORKID
        AND vputs.DATAAREAID = 'ha'
        AND wktbl.[PARTITION] = vputs.[PARTITION]
    WHERE vputs.STATUS = 0
),
PendingPicks AS (
    SELECT COUNT(*) AS PendingVoicePicks
    FROM HAVOICEINTEGRATIONQUEUEPICKS
    WHERE STATUS = 0
)
SELECT
    po.PendingDirect,
    po.PendingAB,
    pp.PendingVoicePicks
FROM PendingOrders po
CROSS JOIN PendingPicks pp;
