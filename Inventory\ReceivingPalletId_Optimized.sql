-- OPTIMIZED VERSION: Receiving Pallet ID Query
-- Author: <PERSON>, optimized on 6/24/2025
-- Major performance improvements:
-- 1. Pre-filter work tables before joins to reduce dataset size
-- 2. Move filters into JOIN conditions for better index usage
-- 3. Add NOLOCK hints for read consistency
-- 4. Fix final JOIN syntax (was using comma join)
-- 5. Optimize join order and eliminate redundant joins

WITH 
-- Pre-filter work lines for line 2 to reduce dataset size
FilteredWorkLine2 AS (
    SELECT 
        wkln.WORKID,
        wkln.DATAAREAID,
        wkln.[PARTITION]
    FROM WHSWORKLINE wkln WITH (NOLOCK)
    WHERE wkln.LINENUM = 2.0
        AND wkln.WORKCLASSID = 'RecPutaway'
        AND wkln.WORKTYPE = 2
        AND wkln.WORKSTATUS = 4
        AND wkln.DATAAREAID = 'ha'  -- Add explicit filter
        AND wkln.[PARTITION] = 5637144576  -- Add explicit filter
),
-- Pre-filter work tables for line 2 to get pallet IDs
Line2Data AS (
    SELECT 
        wktbl.WORKID,
        wktbl.WHSRECEIVEPALLETID AS PalletID
    FROM FilteredWorkLine2 fwl2
    INNER JOIN WHSWORKTABLE wktbl WITH (NOLOCK) 
        ON fwl2.WORKID = wktbl.WORKID 
        AND fwl2.[PARTITION] = wktbl.[PARTITION] 
        AND fwl2.DATAAREAID = wktbl.DATAAREAID
        AND wktbl.WORKTRANSTYPE = 1
        AND wktbl.WHSRECEIVEPALLETID <> ''
        AND wktbl.DATAAREAID = 'ha'
        AND wktbl.[PARTITION] = 5637144576
),
-- Pre-filter work lines for line 3 to reduce dataset size
FilteredWorkLine3 AS (
    SELECT 
        wkln.WORKID,
        wkln.DATAAREAID,
        wkln.[PARTITION]
    FROM WHSWORKLINE wkln WITH (NOLOCK)
    WHERE wkln.LINENUM = 3.0
        AND wkln.WORKTYPE = 1
        AND wkln.WORKSTATUS = 4
        AND wkln.DATAAREAID = 'ha'
        AND wkln.[PARTITION] = 5637144576
),
-- Get line 3 data with pallet IDs, filtered by line 2 existence
Line3Data AS (
    SELECT 
        wktbl.WORKID,
        wktbl.WHSRECEIVEPALLETID AS PalletID
    FROM FilteredWorkLine3 fwl3
    INNER JOIN WHSWORKTABLE wktbl WITH (NOLOCK) 
        ON fwl3.WORKID = wktbl.WORKID 
        AND fwl3.[PARTITION] = wktbl.[PARTITION] 
        AND fwl3.DATAAREAID = wktbl.DATAAREAID
        AND wktbl.WORKTRANSTYPE = 1
        AND wktbl.WHSRECEIVEPALLETID <> ''
        AND wktbl.DATAAREAID = 'ha'
        AND wktbl.[PARTITION] = 5637144576
    -- Only include line 3 records where line 2 exists for the same work
    INNER JOIN Line2Data l2 
        ON l2.WORKID = fwl3.WORKID
        AND l2.PalletID <> wktbl.WHSRECEIVEPALLETID  -- Different pallet IDs
)
-- Final result with proper JOIN syntax
SELECT 
    l2.WORKID, 
    l2.PalletID AS PalletID2,
    l3.PalletID AS PalletID3
FROM Line2Data l2
INNER JOIN Line3Data l3 
    ON l2.WORKID = l3.WORKID
ORDER BY l2.WORKID;  -- Add ordering for consistent results
