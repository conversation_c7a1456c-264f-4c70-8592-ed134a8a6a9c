WITH line2 AS 
(
    SELECT 
        wktbl.WORKID
        , wktbl.WHSRECEIVEPALLETID AS PalletID
         
    FROM WHSWORKLINE wkln
    JOIN WHSWORKTABLE wktbl ON wkln.WORKID = wktbl.WORKID AND wkln.[PARTITION] = wktbl.[PARTITION] AND wkln.DATAAREAID = wktbl.DATAAREAID
    WHERE 
        wkln.LINENUM = 2.0
        AND wkln.WORKCLASSID = 'RecPutaway'
        AND wkln.WORKTYPE = 2
        AND wkln.WORKSTATUS = 4
        AND wktbl.WORKTRANSTYPE = 1
        AND wktbl.WHSRECEIVEPALLETID <> ''
),
line3 AS 
(
    SELECT 
        --*wkln.WORKID
         wktbl.WORKID
        , wktbl.WHSRECEIVEPALLETID AS PalletID
         
    FROM WHSWORKLINE wkln
    JOIN WHSWORKTABLE wktbl ON wkln.WORKID = wktbl.WORKID AND wkln.[PARTITION] = wktbl.[PARTITION] AND wkln.DATAAREAID = wktbl.DATAAREAID
    JOIN line2 ON line2.WORKID = wkln.WORKID
    WHERE 
        wkln.LINENUM = 3.0
        --AND wkln.WORKCLASSID = 'RecPutaway'
        AND wkln.WORKTYPE = 1
        AND wkln.WORKSTATUS = 4
        AND wktbl.WORKTRANSTYPE = 1
        AND wktbl.WHSRECEIVEPALLETID <> ''
        --AND line2.WORKID = wkln.WORKID
        AND line2.PalletID <> wktbl.WHSRECEIVEPALLETID
)
SELECT 
    line2.WORKID, 
    line2.PalletID  AS PalletID2,
    line3.PalletID  AS PalletID3
    
FROM line3, line2