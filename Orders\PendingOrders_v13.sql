-- 3/7/2025
-- Query used on the Power BI report to get the orders that need to be shipped and are not shipped yet
-- The query is used to get the orders that are not shipped yet and are in the following modes: 1st Day, 2nd Day, 3rd Day, Saturday, International Economy
-- Faster than the other versions of the query

--3/14/2025
-- Adding the HoursSinceReleased column to the query
-- Including all orders that are not shipped yet, not only express orders

-- 4/2/2025
-- Adding the WholeSale column to the query

-- 4/9/2025
-- Adding the ShipmentStatus column to the query

-- 5/14/2025 
-- Trying to manually optimize this query.

DECLARE @CurrentDateTime datetime = GETUTCDATE();

WITH orders AS
(
SELECT
    cnttbl.CONTAINERID
    , wst.SHIPMENTID
    , wst.LOADID
    , wst.ORDERNUM
    , st.CUSTGROUP  
    , wst.SHIPMENTSTATUS
    , wst.HALOCDIRECTFAIL
    , wktbl.WORKID
    , wktbl.FROZEN
    , wktbl.WORKSTATUS
    
    , wkusr.USERNAME            AS [LockedUser]
    , voicepick.USERNAME        AS [VoicePickUser]
    , vput.[STATUS]             AS [PutStatus]
    , wkusr1.USERNAME           AS [VoicePutUser]
    , wkct.CLUSTERID
    , wkct.CLUSTERPROFILEID
    , ISNULL(SUM(cntln.QTY), 0)                           AS [CntUnits]
    --, IIF(ISNULL(cnttbl.CONTAINERID, '') = '', 0, CONVERT( DECIMAL( 10, 0), SUM( cntln.QTY ) ) )    AS CntUnits
    , wst.MODECODE
    , wst.WAVEID
    , wvt.WAVETEMPLATENAME
    , ll.Units              AS LoadUnits
    , ll.CREATEDDATETIME    AS [ReleasedToWH]
    , replen.DEMANDWORKID
    
    --, hacs.TRACKINGNUMBER
FROM
    SALESTABLE st
    -- Taking into account orders with shipments created
    JOIN WHSSHIPMENTTABLE wst                   WITH (NOLOCK) ON st.SALESID = wst.ORDERNUM                  AND st.[PARTITION] = wst.[PARTITION]        AND wst.DATAAREAID = st.DATAAREAID 
    AND wst.SHIPMENTSTATUS < 5 -- Not Shipped. If the order is marked as shipped, but has no tracking, the customer will call CCC to check the status of the order.
    AND st.SALESSTATUS = 1 -- Open order
    JOIN WHSWAVETABLE wvt                       WITH (NOLOCK) ON wvt.WAVEID = wst.WAVEID                    AND wvt.[PARTITION] = wst.[PARTITION]       AND wvt.DATAAREAID = wst.DATAAREAID
    LEFT JOIN WHSCONTAINERTABLE cnttbl          WITH (NOLOCK) ON wst.SHIPMENTID = cnttbl.SHIPMENTID         AND cnttbl.[PARTITION] = wst.[PARTITION]    AND cnttbl.DATAAREAID = wst.DATAAREAID
    LEFT JOIN WHSCONTAINERLINE cntln            WITH (NOLOCK) ON cntln.CONTAINERID = cnttbl.CONTAINERID     AND cnttbl.[PARTITION] = cntln.[PARTITION]  AND cnttbl.DATAAREAID = cntln.DATAAREAID
    LEFT JOIN WHSWORKTABLE wktbl                WITH (NOLOCK) ON wktbl.CONTAINERID = cnttbl.CONTAINERID     AND wktbl.[PARTITION] = cnttbl.[PARTITION]  AND wktbl.DATAAREAID = cnttbl.DATAAREAID
    LEFT JOIN WHSWORKCLUSTERLINE wkcl           WITH (NOLOCK) ON wkcl.WORKID = wktbl.WORKID                 AND wkcl.[PARTITION] = wktbl.[PARTITION]    AND wkcl.DATAAREAID = wktbl.DATAAREAID
    LEFT JOIN WHSWORKCLUSTERTABLE wkct          WITH (NOLOCK) ON wkcl.CLUSTERID = wkct.CLUSTERID            AND wkcl.[PARTITION] = wkct.[PARTITION]     AND wkct.DATAAREAID = wkcl.DATAAREAID
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs       WITH (NOLOCK) ON hacs.CARTONID = cnttbl.CONTAINERID         AND hacs.[PARTITION] = cnttbl.[PARTITION]   AND cnttbl.DATAAREAID = hacs.DATAAREAID
    LEFT JOIN WHSWORKUSER wkusr                 WITH (NOLOCK) ON wkusr.USERID = wktbl.LOCKEDUSER            AND wkusr.[PARTITION] = wktbl.[PARTITION]   AND wkusr.DATAAREAID = wktbl.DATAAREAID
    LEFT JOIN HAVOICEINTEGRATIONQUEUEPUTS vput  WITH (NOLOCK) ON wktbl.WORKID = vput.WORKID                 AND wktbl.[PARTITION] = vput.[PARTITION]    AND wktbl.DATAAREAID = vput.DATAAREAID
    LEFT JOIN WHSWORKUSER wkusr1                WITH (NOLOCK) ON wkusr1.USERID = vput.WORKUSER              AND wkusr1.[PARTITION] = vput.[PARTITION]   AND wkusr1.DATAAREAID = vput.DATAAREAID
    -- Replenishment
    LEFT JOIN WHSREPLENWORKLINK replen WITH (NOLOCK) 
        ON replen.DEMANDWORKID = wktbl.WORKID 
    OUTER APPLY (
        SELECT 
            --TOP 1 
            wkuser.USERNAME
        FROM HAVOICEINTEGRATIONQUEUEPICKS vip
        INNER JOIN WHSWORKUSER wkuser WITH (NOLOCK) 
            ON wkuser.USERID = vip.WORKUSER 
            AND wkuser.DATAAREAID = 'ha'
        WHERE vip.WORKID = wktbl.WORKID
        AND vip.STATUS < 7 AND vip.DATAAREAID = 'ha' AND vip.[PARTITION] = wktbl.[PARTITION]
        GROUP BY
            /*vip.WORKID, */vip.[STATUS], wkuser.USERNAME
        --ORDER BY vip.[STATUS] DESC
    ) voicepick
    CROSS APPLY (
    SELECT 
        MIN(CREATEDDATETIME) AS CREATEDDATETIME
        , SUM(QTY) AS Units
    FROM WHSLOADLINE 
    WHERE 
        ORDERNUM = st.SALESID
        --AND LOADID = wst.LOADID 
        AND SHIPMENTID = wst.SHIPMENTID
        AND DATAAREAID = 'ha' AND [PARTITION] = 5637144576
    ) ll
        --AND wktbl.LOCKEDUSER <> ''
WHERE
    --wll.CREATEDDATETIME > GETUTCDATE() - @DaysToCheck
    --wst.SHIPMENTSTATUS < 5 -- Not Shipped. If the order is marked as shipped, but has no tracking, the customer will call CCC to check the status of the order.


    (cnttbl.SHIPCARRIERTRACKINGNUM = '' OR cnttbl.SHIPCARRIERTRACKINGNUM IS NULL) -- Not shipped(including the tracking number added to the table) or tracking number not available
    AND hacs.TRACKINGNUMBER IS NULL -- Not shipped yet
    --AND wst.MODECODE IN ( '1D', '2D', '3D', 'SA', 'IE' ) -- 1st Day, 2nd Day, 3rd Day, Saturday, International Economy
    --AND wst.WAVEID IS NOT NULL -- Wave created
    
    --AND hacs.CREATEDDATETIME IS NULL -- Not shipped yet
    --AND wst.SHIPCONFIRMUTCDATETIME > CONCAT( @DayToCheck, '07:30:00 PM' )
    --AND wst.ORDERNUM = '44426351'
GROUP BY
    cnttbl.CONTAINERID, wst.SHIPMENTID, wst.LOADID, wst.ORDERNUM
    , st.CUSTGROUP, wst.SHIPMENTSTATUS, wst.HALOCDIRECTFAIL
    , wst.MODECODE, wktbl.WORKID, wktbl.FROZEN, wktbl.WORKSTATUS
    , wkusr.USERNAME, voicepick.USERNAME, vput.[STATUS], wkusr1.USERNAME
    , wkct.CLUSTERID, wkct.CLUSTERPROFILEID
    , wst.WAVEID, wvt.WAVETEMPLATENAME
    , ll.Units, ll.CREATEDDATETIME
    , replen.DEMANDWORKID
)
SELECT 
    ORDERNUM                                                    AS [OrderNum]
    , CASE WHEN CUSTGROUP LIKE 'W%' THEN 'Yes' ELSE 'No' END    AS [WholeSale] 
    , MODECODE                                                  AS [ShipMethod]
    , CASE 
        WHEN 
            MODECODE IN ( '1D', '2D', '3D', 'SA', 'IE' ) 
            THEN 'Yes' ELSE 'No' 
    END                                                         AS [IsRush]
    , FORMAT(ReleasedToWH AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 'MMM dd yyyy hh:mmtt', 'en-US')  AS [ReleasedToWH]
    , FORMAT(ReleasedToWH, 'MMM dd, yyyy')                      AS [SLA_Date] -- Formatting as String, for consistency on PBI
    , DATEDIFF(HH, ReleasedToWH, @CurrentDateTime)              AS [HoursSinceReleased]
    -- Use the container units if available
    --, CASE WHEN CntUnits > 0 THEN CntUnits ELSE LoadUnits END   AS [Units]

    , CAST(IIF(CntUnits > 0, CntUnits, LoadUnits) AS INT)  AS [Units]
    , SHIPMENTID                                                AS [ShipmentId]
    ,  CASE 
		--WHEN wst.SHIPMENTSTATUS IS NULL THEN 'Not released'
		WHEN SHIPMENTSTATUS = 0 THEN 	'Open'
		WHEN SHIPMENTSTATUS = 1 THEN 	'Waved'
		WHEN SHIPMENTSTATUS = 2 THEN 	'In process'
		WHEN SHIPMENTSTATUS = 3 THEN 	'In packing'
		WHEN SHIPMENTSTATUS = 4 THEN 	'Loaded'
		WHEN SHIPMENTSTATUS = 5 THEN 	'Shipped'
		ELSE 							'Received'
	END                                                         AS ShipmentStatus -- Adding it for cross-reference with the other report(Outbound)
    , IIF(HALOCDIRECTFAIL = 1, 'Yes', 'No')                     AS [LDF]
    , COALESCE(CONTAINERID, 'N/A')                              AS [ContainerId]
    , COALESCE(WORKID, 'N/A')                                   AS [WorkId]
    , IIF(DEMANDWORKID IS NOT NULL, 'Yes', 'No')                AS [NeedsReplen]
    , CASE 
            WHEN FROZEN = 1 THEN 'Yes' 
            WHEN FROZEN = 0 THEN 'No' 
            ELSE 'N/A' 
        END                                                     AS [WorkBlocked]
    , CASE 
        WHEN WORKSTATUS = 0 THEN 'Open' 
        WHEN WORKSTATUS = 1 THEN 'In process'
        WHEN WORKSTATUS = 4 THEN 'Closed'
        ELSE 'N/A'
    END                                                         AS [WorkStatus]
    , COALESCE(LockedUser,'N/A')                                AS [LockedBy]
    , COALESCE(VoicePutUser, VoicePickUser,'N/A')               AS [VoiceUser]
    , CASE 
        WHEN PutStatus IS NULL THEN CASE WHEN VoicePickUser IS NULL THEN 'N/A' ELSE 'In Progress' END --
        ELSE 
            CASE 
                WHEN PutStatus = 0 THEN 'Pending'
                WHEN PutStatus = 2 THEN 'Completed'
                WHEN PutStatus = 3 THEN 'Error' -- Not sure if this is the correct status
                WHEN PutStatus = 4 THEN 'Reset'
                WHEN PutStatus = 5 THEN 'Manually Picked'
                WHEN PutStatus = 6 THEN 'Canceled'
                ELSE 'N/A'
            END END                                             AS [VoiceStatus]
    , COALESCE(CLUSTERID, 'N/A')                                AS [ClusterId]
    , COALESCE(CLUSTERPROFILEID, 'N/A')                         AS [ClusterProfile]
    , WAVEID                                                    AS [WaveId]
    , [WAVETEMPLATENAME]                                        AS [WaveTemplate]
FROM
    orders
WHERE
    1 = 1
    --AND ORDERNUM IN ('48696854', '48697593','48697302', '48698332','48698234','48698311','48698038')
ORDER BY
    HoursSinceReleased DESC;