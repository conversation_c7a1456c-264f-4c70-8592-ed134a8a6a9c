SELECT TOP (1000) [cntnr_name]
      ,[cntnr_type]
      ,[Scanner_num]
      ,[Sort_code]
      ,[destination]
      ,[fail_reason]
      ,[cntnr_detail_1]
      ,[cntnr_detail_2]
      ,[cntnr_detail_3]
      ,[cntnr_detail_4]
      ,[cntnr_detail_5]
      ,[status]
      ,[date_stamp] -- EDT
  FROM [Conveyor].[dbo].[WMS_EXA_IMP_DIV_CNFRM]
  WHERE
    1 = 1 
    AND cntnr_name IN ('CN018668948', 'CN018670038', 'CN018669162', 'CN018669134', 'CN018669164', 'CN018669093', -- Good
    'CN018663342', 'CN018663252', 'CN018663356', 'CN018663246','CN018663298', 'CN018663466',
    'CN018663587', 'CN018663514', 'CN018663520', 'CN018663531', 'CN018663775', 'CN018663525')
