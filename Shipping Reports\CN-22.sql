/*
Used originally to identify international shipments
*/

DECLARE @DaysToCheck INT = 10;

USE DAX_PROD

SELECT
    shptbl.SHIPMENTID
    , shptbl.ORDERNUM
    , cnttbl.CONTAINERID
    , shptbl.DELIVERYNAME
    , shptbl.MODECODE AS ShpDelivMode
    , cnttbl.HAWHSSHIPPERID AS ShipperId
    , hsmt.SHIPMETHODDESCRIPTION
    , CASE WHEN shptbl.HASINGLESKU = 1 THEN 'Single' Else 'Multi' END AS PckgType
    , lpa.CITY
    , lpa.[STATE]
    , CASE 
        WHEN lpa.[STATE] = 'AS' THEN 'American Samoa'
        WHEN lpa.[STATE] = 'GU' THEN 'Guam'
        WHEN lpa.[STATE] = 'MP' THEN 'North Mariana Islands'
        WHEN lpa.[STATE] = 'MH' THEN 'Marshall Islands'
        WHEN lpa.[STATE] = 'FM' THEN 'Federated States of Micronesia'
        WHEN lpa.[STATE] = 'PW' THEN 'Palau'
        WHEN lpa.[STATE] = 'UM' THEN 'Wake Island'
        WHEN lpa.[STATE] = 'AA' THEN 'Armed Forces Americas'
        WHEN lpa.[STATE] = 'AE' THEN 'Armed Forces Europe'
        WHEN lpa.[STATE] = 'AP' THEN 'Armed Forces Pacific'
        ELSE 'Military'
      END AS 'State Name'
    , shptbl.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS ModifiedDT

FROM
    WHSSHIPMENTTABLE shptbl
    LEFT JOIN LOGISTICSPOSTALADDRESS lpa ON lpa.RECID = shptbl.DELIVERYPOSTALADDRESS AND lpa.[PARTITION] = shptbl.[PARTITION]
    JOIN WHSCONTAINERTABLE cnttbl ON cnttbl.SHIPMENTID = shptbl.SHIPMENTID AND cnttbl.[PARTITION] = shptbl.[PARTITION] AND cnttbl.DATAAREAID = shptbl.DATAAREAID
    JOIN [HASHIPMETHODTRANSLATION] hsmt ON	hsmt.HAWHSSHIPMETHOD = cnttbl.HAWHSSHIPMETHOD AND hsmt.DATAAREAID = cnttbl.DATAAREAID
WHERE
    rtrim(ltrim(lpa.[STATE])) IN ( 'AS', 'GU', 'MP', 'MH', 'FM','PW', 'UM', 'AA', 'AE', 'AP' )
    -- OR lpa.CITY IN ( 'APO', 'FPO', 'DPO' ))
    --AND shptbl.MODIFIEDDATETIME BETWEEN '1/1/2023' AND '8/11/2023'
    AND shptbl.MODIFIEDDATETIME  > DATEADD( DAY, -@DaysToCheck, GETUTCDATE() )
    AND shptbl.DELIVERYPOSTALADDRESS > 0
    --AND shptbl.SHIPMENTSTATUS < 5 -- Not shipped
    --AND ISNULL( shptbl.LOADID, '' ) <> '' -- Work created

/*
    SELECT
        TOP 20 *
    FROM
        LOGISTICSPOSTALADDRESS
sp_columns WHSSHIPMENTTABLE
*/