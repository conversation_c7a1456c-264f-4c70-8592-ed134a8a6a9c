-- Find shipments with non-ASCII characters in DELIVERYNAME and ADDRESS fields
SELECT 
    wst.SHIPMENTID  AS AX_ShipmentID,
    wst.ORDERNUM    AS AX_OrderNum,
    st.hadwsalesid AS SFCCOrderID,
    wst.ACCOUNTNUM,
    st.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS OrderCreatedDateEST,
    --wst.LOADID,
    wst.DELIVERYNAME,
    wst.ADDRESS,
    wst.MODECODE AS ShipMethod,
    wst.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS LastModifiedEST,
    -- Character analysis for DELIVERYNAME
    --<PERSON>EN(wst.DELIVERYNAME) AS DeliveryNameLength,
    --LEN(CAST(wst.DELIVERYNAME AS VARCHAR(100))) AS DeliveryNameASCIILength,
    -- Character analysis for ADDRESS
    --<PERSON>EN(wst.ADDRESS) AS AddressLength,
    --<PERSON><PERSON>(CAST(wst.ADDRESS AS VARCHAR(250))) AS AddressASCIILength,
    -- Shipment status
    CASE 
        WHEN wst.SHIPMENTSTATUS = 0 THEN 'Open'
        WHEN wst.SHIPMENTSTATUS = 1 THEN 'Waved'
        WHEN wst.SHIPMENTSTATUS = 2 THEN 'In process'
        WHEN wst.SHIPMENTSTATUS = 3 THEN 'In packing'
        WHEN wst.SHIPMENTSTATUS = 4 THEN 'Loaded'
        WHEN wst.SHIPMENTSTATUS = 5 THEN 'Shipped'
        WHEN wst.SHIPMENTSTATUS = 6 THEN 'Received'
    END AS ShipmentStatus
FROM WHSSHIPMENTTABLE wst WITH (NOLOCK)
JOIN SALESTABLE st ON st.SALESID = wst.ORDERNUM AND st.DATAAREAID = wst.DATAAREAID AND st.PARTITION = wst.PARTITION
WHERE wst.DATAAREAID = 'ha'
    AND wst.PARTITION = 5637144576
    AND wst.LOADDIRECTION = 2  -- Outbound shipments
    AND (
        -- Non-ASCII in DELIVERYNAME
        (wst.DELIVERYNAME IS NOT NULL 
         AND wst.DELIVERYNAME != CAST(wst.DELIVERYNAME AS VARCHAR(100)))
        OR
        -- Non-ASCII in ADDRESS
        (wst.ADDRESS IS NOT NULL 
         AND wst.ADDRESS != CAST(wst.ADDRESS AS VARCHAR(250)))
    )
    AND wst.MODIFIEDDATETIME > DATEADD(DAY, -180, GETUTCDATE())
    AND wst.SHIPMENTSTATUS = 5 -- Shipped
ORDER BY wst.MODIFIEDDATETIME;


