SELECT [Order_LPN]
      ,[Pick_Batch]
      ,[Actual_Weight]
      ,[Operator_ID]
      ,[Machine_Number]
      ,[Completion_Date_Time]
      ,[Completion_Code]
      ,DATEADD(hh, 3, [date_time]) AS KY_DT -- PDT
  FROM [Autobagger].[dbo].[Order_Uploads_Log]
  WHERE
    1 = 1 
  AND date_time BETWEEN '2025-08-16 21:00:00.000' AND '2025-08-17 20:59:59.999'
  AND Operator_ID LIKE 'ms%'
  --AND Operator_ID NOT LIKE 'bcs%'
  --AND Operator_ID <> 'automatic'
  /*
  AND Order_LPN IN ('CN018668948', 'CN018670038', 'CN018669162', 'CN018669134', 'CN018669164', 'CN018669093', -- Manual station
    'CN018663342', 'CN018663252', 'CN018663356', 'CN018663246','CN018663298', 'CN018663466',
    'CN018663587', 'CN018663514', 'CN018663520', 'CN018663531', 'CN018663775', 'CN018663525')  -- Not updated
    */
  ORDER BY
    date_time ASC
