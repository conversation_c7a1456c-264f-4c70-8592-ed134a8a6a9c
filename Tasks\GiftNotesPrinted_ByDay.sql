

--giftnotesprinted

/*
Getting a report for gift notes printed during 2024, and 2025. Sam's request
*/

--DECLARE @DaysToReport INT = 180;
DECLARE @ArchiveStartDate DATETIME = '01/01/2024 05:00:00 AM';
DECLARE @ArchiveEndDate DATETIME = '07/01/2025 04:00:00 AM';

WITH GiftNotesPrinted AS
(
    -- Archive data
    SELECT 
        CAST(shptbl.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS DATE) AS ShippedDate,
        SUM(CASE WHEN shptbl.HASINGLESKU = 1 AND shptbl.HAQUERYGIFTNOTE = 1 THEN 1 ELSE 0 END) AS [SinglesWithGiftNote],
        SUM(shptbl.HASINGLESKU) AS [TotalSingles],
        SUM(CASE WHEN shptbl.HASINGLESKU = 0 AND shptbl.HAQUERYGIFTNOTE = 1 THEN 1 ELSE 0 END) AS [MultisWithGiftNote],
        COUNT(*) AS TotalShipments
    FROM [DAX_Archive].[arc].WHSSHIPMENTTABLE shptbl WITH (NOLOCK)
    WHERE shptbl.LOADDIRECTION = 2 -- Outbound
        AND shptbl.INVENTLOCATIONID = '4010' -- KY DC
        AND shptbl.WORKTRANSTYPE = 2 -- Sales order
        AND shptbl.DATAAREAID = 'ha'
        AND shptbl.[PARTITION] = 5637144576
        AND shptbl.MODIFIEDDATETIME BETWEEN @ArchiveStartDate AND @ArchiveEndDate
    GROUP BY 
        CAST(shptbl.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS DATE)
    
    UNION ALL
    
    -- Production data
    SELECT 
        CAST(shptbl.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS DATE) AS ShippedDate,
        SUM(CASE WHEN shptbl.HASINGLESKU = 1 AND shptbl.HAQUERYGIFTNOTE = 1 THEN 1 ELSE 0 END) AS [SinglesWithGiftNote],
        SUM(shptbl.HASINGLESKU) AS [TotalSingles],
        SUM(CASE WHEN shptbl.HASINGLESKU = 0 AND shptbl.HAQUERYGIFTNOTE = 1 THEN 1 ELSE 0 END) AS [MultisWithGiftNote],
        COUNT(*) AS TotalShipments
    FROM [DAX_PROD].[dbo].WHSSHIPMENTTABLE shptbl WITH (NOLOCK)
    WHERE shptbl.LOADDIRECTION = 2
        AND shptbl.INVENTLOCATIONID = '4010'
        AND shptbl.WORKTRANSTYPE = 2
        AND shptbl.DATAAREAID = 'ha'
        AND shptbl.[PARTITION] = 5637144576
        AND shptbl.MODIFIEDDATETIME > @ArchiveEndDate
    GROUP BY 
        CAST(shptbl.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS DATE)
),
AggregatedData AS
(
    SELECT 
        ShippedDate,
        SUM([SinglesWithGiftNote]) AS [SinglesWithGiftNote],
        SUM([TotalSingles]) AS [TotalSingles],
        SUM([MultisWithGiftNote]) AS [MultisWithGiftNote],
        SUM(TotalShipments) AS TotalShipments
    FROM GiftNotesPrinted
    GROUP BY ShippedDate
)
SELECT TOP 60
    /*FORMAT(ShippedDate, 'MMM-yyyy') AS ShippedMonth,
    FORMAT(ShippedDate, 'dd') AS ShippedDay,*/
    ShippedDate,
    [SinglesWithGiftNote],
    CAST(([SinglesWithGiftNote] * 100.0 / NULLIF(TotalShipments, 0)) AS DECIMAL(10,2)) AS [PercentageOfSinglesWithGiftNote],
    [MultisWithGiftNote],
    CAST(([MultisWithGiftNote] * 100.0 / NULLIF(TotalShipments, 0)) AS DECIMAL(10,2)) AS [PercentageOfMultisWithGiftNote],
    [SinglesWithGiftNote] + [MultisWithGiftNote] AS [TotalWithGiftNote],
    TotalShipments,
    CAST((([SinglesWithGiftNote] + [MultisWithGiftNote]) * 100.0 / NULLIF(TotalShipments, 0)) AS DECIMAL(10,2)) AS [TotalPercentageWithGiftNote]
FROM AggregatedData
ORDER BY TotalWithGiftNote DESC;
