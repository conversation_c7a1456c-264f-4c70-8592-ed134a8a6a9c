-- Modifying on 5/1/2025 to take into account rush orders going thru the AutoBagger
-- Modifying on 7/24/2025 to optimize the query

DECLARE @DaysToCheck INT = 10; -- Number of days to check for rush orders
DECLARE @TruckGone NVARCHAR(20) = ' 17:15:00.000'; -- Time when the truck leaves the building
DECLARE @CutOffTime NVARCHAR(20) = ' 14:20:00.000'; -- At this time, all the rush orders should be released to the WH


WITH rush AS
(
SELECT
    wst.ORDERNUM
    , wst.ACCOUNTNUM
    , wst.SHIPMENTID
    , cnttbl.CONTAINERID
    , wst.MODECODE
    , wst.WAVEID
    , wst.DELIVERYNAME                                                                      AS ShipTo
    , MIN( wll.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' )    AS  ReleasedToWH
    , wst.SHIPMENTSTATUS                                                                    AS ShipmentStatus
    , hacs.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'          AS ManualShippedTime
    , hacs.SHIPPERID                                                                        AS ManualShipperId
    , cnttbl.SHIPCARRIERTRACKINGNUM                                                         AS CntTrackingNum
    , cnttbl.MASTERTRACKINGNUM                                                              AS CntMasterTrackingNum
    , cnttbl.HAWHSSHIPPERID                                                                 AS CntShipperId      
    , cnttbl.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'       AS CntModifiedDateTime
    , wst.SHIPCONFIRMUTCDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'    AS KYShipConfirmDateTime
    , wst.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'          AS KYModifiedDateTime
    , CONVERT( DECIMAL( 10, 0), SUM( cntln.QTY ) )                                            AS Units
    --, *
FROM
    WHSSHIPMENTTABLE wst WITH (NOLOCK)
    JOIN WHSLOADLINE wll WITH (NOLOCK) 
        ON wll.SHIPMENTID = wst.SHIPMENTID                        -- I_102664ITEMDIMIDIDX
        AND wll.[PARTITION] = wst.[PARTITION]                     
        AND wll.DATAAREAID = wst.DATAAREAID                       
        AND wll.LOADID = wst.LOADID         
        AND wll.ORDERNUM = wst.ORDERNUM 

    JOIN WHSCONTAINERLINE cntln WITH (NOLOCK) 
        ON cntln.[PARTITION] = wst.[PARTITION]                    -- I_102619SHIPMENTIDCONTAINERIDIDX
        AND cntln.DATAAREAID = wst.DATAAREAID                     
        AND cntln.SHIPMENTID = wst.SHIPMENTID                     
        AND cntln.ITEMID = wll.ITEMID 
        AND cntln.INVENTDIMID = wll.INVENTDIMID

    JOIN WHSCONTAINERTABLE cnttbl WITH (NOLOCK)
        ON cnttbl.[PARTITION] = cntln.[PARTITION]                 -- I_102620SHIPMENTIDCONTAINERIDIDX
        AND cnttbl.DATAAREAID = cntln.DATAAREAID                  
        AND cnttbl.SHIPMENTID = cntln.SHIPMENTID                  
        AND cnttbl.CONTAINERID = cntln.CONTAINERID                

    LEFT JOIN HASHIPPEDCARTONSTAGING hacs WITH (NOLOCK) 
        ON hacs.CARTONID = cnttbl.CONTAINERID 
        AND hacs.[PARTITION] = cnttbl.[PARTITION] 
        AND hacs.DATAAREAID = cnttbl.DATAAREAID
WHERE wst.DATAAREAID = 'ha'
    AND wst.[PARTITION] = **********
    AND wst.MODECODE IN ('1D', '2D', '3D', 'SA', 'IE')
    AND wll.CREATEDDATETIME > DATEADD(DAY, -@DaysToCheck, GETUTCDATE())
GROUP BY
    wst.ORDERNUM, wst.ACCOUNTNUM, wst.SHIPMENTID, wst.LOADID, cnttbl.CONTAINERID, wst.MODECODE,
    wst.DELIVERYNAME, wst.SHIPCONFIRMUTCDATETIME, wst.MODIFIEDDATETIME, wst.WAVEID,
    cnttbl.SHIPCARRIERTRACKINGNUM, cnttbl.MODIFIEDDATETIME, cnttbl.HAWHSSHIPPERID, cnttbl.MASTERTRACKINGNUM,
    hacs.CREATEDDATETIME, hacs.SHIPPERID, wst.SHIPMENTSTATUS,
    CAST(wll.CREATEDDATETIME AS DATE)
),
rushp AS
(
SELECT
    OrderNum
    , ACCOUNTNUM
    , SHIPMENTID
    , CONTAINERID
    , MODECODE
    , WAVEID
    , ShipTo
    , DATENAME(dw, ReleasedToWH)    AS DayReleased
    , ReleasedToWH
    , CASE 
        WHEN DATEPART(w, ReleasedToWH) IN (2, 3, 4, 5, 6)  -- Monday-Friday
         THEN 
            CASE
                WHEN ReleasedToWH  < CAST( CAST( CAST(ReleasedToWH AS DATE) AS nvarchar(10) ) + @CutOffTime AS datetime2) AT TIME ZONE 'Eastern Standard Time' 
                    THEN -- Before the batch jobs runs at 2:10pm
                        CAST( CAST( CAST(ReleasedToWH AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time' 
                ELSE -- released after the batch job ran(> 2:20 pm). Should go the following weekday
                   CASE 
                     WHEN DATEPART(w, ReleasedToWH) IN (2, 3, 4, 5)  -- Monday-Thursday. Should go the following day
                        THEN 
                            CAST( CAST( CAST((DATEADD(DAY, 1, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
                        ELSE -- Friday. Should go on Monday
                            CAST( CAST( CAST((DATEADD(DAY, 3, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
                    END
            END
         ELSE
            CASE 
                WHEN DATEPART(w, ReleasedToWH) = 7 -- Saturday
                    THEN
                        CAST( CAST( CAST((DATEADD(DAY, 2, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
                    ELSE -- Sunday
                        CAST( CAST( CAST((DATEADD(DAY, 1, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
            END
    END AS "ExpectedShipTime"
    , CntTrackingNum
    , CntMasterTrackingNum
    , ShipmentStatus
    , KYShipConfirmDateTime
    , ManualShippedTime
    , CntModifiedDateTime
    , CASE 
         -- If the order was shipped manually, use that time
        WHEN ManualShippedTime IS NOT NULL THEN ManualShippedTime
        -- With tracking number updated by the AB, most probable case, or manually updated
        --WHEN LEN(RTRIM(LTRIM(CntTrackingNum))) > 0 THEN CntModifiedDateTime 
        WHEN CntTrackingNum <> '' THEN CntModifiedDateTime -- Tracking number in the container
        -- Handling the cases where the work is manually completed and the shipment is not updated with the tracking number
        -- CN018220779, for example, the work was manually completed and the tracking number was not updated in the container table
        WHEN 
            ShipmentStatus = 5 -- Shipped
            AND CntMasterTrackingNum <> '' -- Master tracking number in the container
        THEN CntModifiedDateTime 
        ELSE NULL --ManualShippedTime
    END AS ActualShippedTime
    , CntShipperId
    , ManualShipperId
    , Units
FROM
    rush
),
rushf AS
(
SELECT 
    ORDERNUM
    , ACCOUNTNUM 
    , SHIPMENTID
    , CONTAINERID
    , Units
    , MODECODE  AS ShipMethod
    , ShipTo
    , WAVEID
    , DayReleased
    , ReleasedToWH       AS [ReleasedToWH_DT]
    , FORMAT(ReleasedToWH, 'MMM dd, yyyy hh:mmtt', 'en-US') AS ReleasedToWH
    , FORMAT(ExpectedShipTime, 'MMM dd, yyyy hh:mmtt', 'en-US') AS MustShipBy  
    , ManualShippedTime
    , CntTrackingNum
    , CntModifiedDateTime
    , FORMAT(ActualShippedTime, 'MMM dd, yyyy hh:mmtt', 'en-US') AS ActualShippedTime
    , ManualShipperId
    , CntShipperId
    , CASE 
        WHEN ManualShipperId IS NOT NULL THEN ManualShipperId -- Manual shipment already updated with the shipper id
        --WHEN LEN(RTRIM(LTRIM(CntShipperId))) > 0 THEN CntShipperId -- ContainerTable already updated with the shipper id
        WHEN CntShipperId <> '' THEN CntShipperId -- ContainerTable already updated with the shipper id
        ELSE NULL END AS ShipperId
    , KYShipConfirmDateTime
    , CASE 
        WHEN ActualShippedTime IS NULL  THEN 'No'   
        WHEN CAST(ActualShippedTime AS DATETIME2) > CAST(ExpectedShipTime AS DATETIME2) THEN 'No' ELSE 'Yes' END AS ShippedOnTime
FROM
    rushp
)
SELECT 
    ORDERNUM            AS [OrderNum]
    --, ACCOUNTNUM        AS [AccountNum]
    --, SHIPMENTID        AS [ShipmentId]
    , Units
    , ShipMethod
    , CONTAINERID       AS [ContainerId]
    --, ShipTo
    --, WAVEID            AS [WaveId]
    --, DayReleased
    
    --, ExpectedShipTime   AS [ExpectedShipTime]
    , ReleasedToWH
    --, CntTrackingNum
    , MustShipBy
    --, ManualShippedTime
    --, CntModifiedDateTime
    , ActualShippedTime
    --, ManualShipperId
    --, CntShipperId
    , ShipperId 
    --, KYShipConfirmDateTime 
    , ShippedOnTime
FROM rushf
WHERE
    1 = 1
    --AND ShippedOnTime = 'Yes'
ORDER BY
    ReleasedToWH_DT -- to maintain the original order of the query

--sp_columns WHSCONTAINERTABLE

--sp_indexes WHSCONTAINERTABLE;
