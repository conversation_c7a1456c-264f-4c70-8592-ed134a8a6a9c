
-- Checking rush orders
/*
    Finding out what is shipped on time or no
    Planning to add more details about the workid, the container, blocked or no
    Planning to use HASHIppedCartonStaging better - Done
*/
-- 

DECLARE @DaysToCheck INT = 5;
DECLARE @TruckGone NVARCHAR(20) = ' 17:25:00.000';
DECLARE @CutOffTime NVARCHAR(20) = ' 14:20:00.000';


--SELECT DATEPART(w, GETDATE()-3)
--SELECT DATEADD(dd, 0, DATEDIFF(dd, 0, GETDATE()))
--SELECT CONCAT( @DayToCheck, '06:09:00 PM' )
--SELECT CAST( CAST( CAST(GETDATE() AS DATE) AS nvarchar(10) ) + ' 05:30:00 PM' AS datetime)

WITH rush AS
(
SELECT
    wst.ORDERNUM
    , wst.ACCOUNTNUM
   -- , st.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'         AS KYCreatedDate
    , wst.SHIPMENTID
    , cnttbl.CONTAINERID
    , wst.MODECODE
    , wst.WAVEID
    , wst.DELIVERYNAME                                                                      AS ShipTo
    , MIN( wll.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' )    AS  ReleasedToWH
    , wst.SHIPCONFIRMUTCDATETIME 
    , hacs.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'          AS ActualShippedTime
    , wst.SHIPCONFIRMUTCDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'    AS KYShipConfirmDateTime
    , wst.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'          AS KYModifiedDateTime
    , CONVERT( DECIMAL( 10, 0), SUM( cntln.QTY ) )                                            AS Units
    --, *
FROM
    WHSCONTAINERTABLE cnttbl 
    JOIN WHSCONTAINERLINE cntln ON cntln.CONTAINERID = cnttbl.CONTAINERID  
    JOIN WHSSHIPMENTTABLE wst ON cnttbl.SHIPMENTID = wst.SHIPMENTID
    JOIN WHSLOADLINE wll ON wll.SHIPMENTID = wst.SHIPMENTID AND wll.LOADID = wst.LOADID AND wll.ORDERNUM = wst.ORDERNUM 
        AND wll.ITEMID = cntln.ITEMID AND wll.INVENTDIMID = cntln.INVENTDIMID-- AND cntln.CONTAINERID = cnttbl.CONTAINERID AND cnttbl.SHIPMENTID = wst.SHIPMENTID
    --JOIN SALESTABLE st ON st.SALESID = wst.ORDERNUM AND st.[PARTITION] = wst.[PARTITION] AND wst.DATAAREAID = st.DATAAREAID 
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs ON hacs.CARTONID = cnttbl.CONTAINERID AND hacs.[PARTITION]    = 5637144576 AND hacs.DATAAREAID    = 'ha'
WHERE
    wll.CREATEDDATETIME > GETUTCDATE() - @DaysToCheck
    AND wst.MODECODE IN ( '1D', '2D', '3D', 'SA', 'IE' )
    AND cnttbl.[PARTITION]  = 5637144576 AND cnttbl.DATAAREAID  = 'ha'
    AND cntln.[PARTITION]   = 5637144576 AND cntln.DATAAREAID   = 'ha'
    AND wst.[PARTITION]     = 5637144576 AND wst.DATAAREAID     = 'ha'
    AND wll.[PARTITION]     = 5637144576 AND wll.DATAAREAID     = 'ha'
    
    /*
    Claude 3.7 Sonnet
    The reasons this is better:

Improved Index Usage: SQL Server can use the indexes on DATAAREAID and PARTITION more efficiently when filtering on constant values rather than comparing between tables.
Better Optimizer Decisions: When SQL Server knows the exact values, it can make better cardinality estimates and potentially push these filters before the join operation.
Reduced Join Complexity: Comparing fewer columns in the join condition (just the meaningful FIELD) simplifies the join operation.
Potential for Better Query Plans: SQL Server may be able to use more efficient access methods when filtering on constants, especially if statistics are well-maintained.

For maximum performance, make sure your indexes include these columns in the optimal order. For commonly used queries, consider creating composite indexes that include FIELD, 
DATAAREAID, and PARTITION in an order that best supports your query patterns.
    */
    --AND wst.SHIPCONFIRMUTCDATETIME > CONCAT( @DayToCheck, '07:30:00 PM' )
    --AND wst.ORDERNUM = '********'
GROUP BY
    cnttbl.CONTAINERID, wst.SHIPMENTID, wst.LOADID, wst.ORDERNUM, wst.ACCOUNTNUM, wst.MODECODE --, st.DLVMODE, st.CREATEDDATETIME
    , CAST( wll.CREATEDDATETIME AS DATE) , wst.DELIVERYNAME, wst.SHIPCONFIRMUTCDATETIME, wst.MODIFIEDDATETIME, hacs.CREATEDDATETIME,  wst.WAVEID
),
rushp AS
(
SELECT
    OrderNum
    , ACCOUNTNUM
    , SHIPMENTID
    , CONTAINERID
    , MODECODE
    , WAVEID
    , ShipTo
    , DATENAME(dw, ReleasedToWH)    AS DayReleased
    , ReleasedToWH
        -- , CAST( CAST( CAST(ReleasedToWH AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT Time Zone 'Eastern Standard Time' AS CalcTime
    , CASE 
        WHEN DATEPART(w, ReleasedToWH) IN (2, 3, 4, 5, 6)  -- Monday-Friday
         THEN 
            CASE
                WHEN ReleasedToWH  < CAST( CAST( CAST(ReleasedToWH AS DATE) AS nvarchar(10) ) + @CutOffTime AS datetime2) AT TIME ZONE 'Eastern Standard Time' 
                    THEN -- Before the batch jobs runs at 2:10pm
                        CAST( CAST( CAST(ReleasedToWH AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time' 
                ELSE -- released after the batch job ran(> 2:20 pm). Should go the following weekday
                   CASE 
                     WHEN DATEPART(w, ReleasedToWH) IN (2, 3, 4, 5)  -- Monday-Thursday. Should go the following day
                        THEN 
                            CAST( CAST( CAST((DATEADD(DAY, 1, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
                        ELSE -- Friday. Should go on Monday
                            CAST( CAST( CAST((DATEADD(DAY, 3, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
                    END
            END
         ELSE
            CASE 
                WHEN DATEPART(w, ReleasedToWH) = 7 -- Saturday
                    THEN
                        CAST( CAST( CAST((DATEADD(DAY, 2, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
                    ELSE 
                        CAST( CAST( CAST((DATEADD(DAY, 1, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
            END
    END AS "ExpectedShipTime"
    , KYShipConfirmDateTime
    , ActualShippedTime
    , Units
FROM
    rush
)
SELECT 
    ORDERNUM
    , ACCOUNTNUM 
    , SHIPMENTID
    , CONTAINERID
    , Units
    , MODECODE  AS ShipMethod
    , ShipTo
    , WAVEID
    , DayReleased
    , ReleasedToWH
    , ExpectedShipTime  AS MustShipBy
    , ActualShippedTime
    , KYShipConfirmDateTime
    , --CASE WHEN DATEPART(yy,ActualShippedTime) = 1899 THEn 'No' 
      CASE WHEN ActualShippedTime IS NULL  THEN 'No'   
       WHEN CAST(ActualShippedTime AS DATETIME2) > CAST(ExpectedShipTime AS DATETIME2) THEN 'No' ELSE 'Yes' END AS ShippedOnTime
FROM
    rushp
--ORDER BY
    --ReleasedToWH
    --ORDERNUM
/*
sp_columns WHSCONTAINERTABLE
sp_columns WHSSHIPMENTTABLE
sp_columns HASHIPPEDCARTONSTAGING
*/