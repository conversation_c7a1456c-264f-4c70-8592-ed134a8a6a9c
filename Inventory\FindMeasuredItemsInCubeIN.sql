
/*
Classifying the boxews in CubeIn as measured or not measured to save labor
*/
SELECT 
    wkln.LOCATEDLPID AS [LP]
    , CASE WHEN physd.weight > 0.0 AND physd.DEPTH > 0.0 AND physd.WIDTH > 0.0 AND physd.HEIGHT > 0.0 THEN 'Yes' ELSE 'No' END AS [Measured]
    , wkln.WORKID AS [WorkId]
    , wkln.ORDERNUM AS [Order]
    , wkln.LOADID AS [Load]
    , wkln.ITEMID AS [Item]
    , idim.INVENTCOLORID AS [Color]
    , idim.INVENTSIZEID AS [Size]
    , CAST(wkln.QTYWORK AS INT)AS [Qty]
        
    /*, physd.weight AS [Weight]
    , physd.depth AS [Depth]
    , physd.width AS [Width]
    , physd.height AS [Height]*/
FROM
    WHSWORKLINE wkln
    JOIN inventdim idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
    JOIN WHSPHYSDIMUOM physd ON physd.ITEMID = wkln.ITEMID AND physd.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physd.ECORESITEMSIZENAME = idim.INVENTSIZEID
        AND physd.DATAAREAID = wkln.DATAAREAID AND physd.[PARTITION] = wkln.[PARTITION]
WHERE
    wkln.Linenum = 3.0
    AND wkln.wmslocationid = 'CubeIN'
    AND wkln.WORKSTATUS = 0
    AND wkln.WORKTYPE = 1
    AND wkln.DATAAREAID = 'ha'
    AND wkln.[PARTITION] = 5637144576
    --AND wkln.ITEMID = '83334'

--sp_columns whsphysdimuom