-- Finding orders with missing tracking from 8/16/2025
-- Ordered by distance from zip code 40229 (Louisville, KY area)

SELECT
  shptbl.ORDERNUM AS [AX_Order]
  , st.HADWSALESID AS [SFCC_Order]
  , st.EMAIL AS [Email]
  , shptbl.ACCOUNTNUM As [AccountNum]
  , shptbl.MODECODE [ShipMethod]
  , shptbl.DELIVERYNAME AS [Name]
  , lpa.CITY
  , lpa.[STATE]
  , lpa.ZIPCODE
  , cnttbl.MASTERTRACKINGNUM AS [AB_Tracking]
  , ABS(CAST(lpa.ZIPCODE AS INT) - 40229) AS [ZipDistance]
FROM
  WHSSHIPMENTTABLE shptbl
JOIN LOGISTICSPOSTALADDRESS lpa ON lpa.RECID = shptbl.DELIVERYPOSTALADDRESS AND lpa.[PARTITION] = shptbl.[PARTITION]
JOIN SALESTABLE st ON st.SALESID = shptbl.ORDERNUM AND st.DATAAREAID = shptbl.DATAAREAID AND st.[PARTITION] = shptbl.[PARTITION]
JOIN WHSCONTAINERTABLE cnttbl ON cnttbl.SHIPMENTID = shptbl.SHIPMENTID AND cnttbl.[PARTITION] = shptbl.[PARTITION] AND cnttbl.DATAAREAID = shptbl.DATAAREAID
WHERE
  1 = 1
  AND shptbl.INVENTLOCATIONID = '4010'
  AND shptbl.WORKTRANSTYPE = 2
  AND shptbl.DATAAREAID = 'ha'
  AND shptbl.[PARTITION] = **********
  AND shptbl.SHIPMENTSTATUS = 2
  AND shptbl.LOADDIRECTION = 2
  AND shptbl.MODIFIEDDATETIME BETWEEN '8/16/2025' AND '8/18/2025'
  AND ISNUMERIC(lpa.ZIPCODE) = 1  -- Only include numeric zip codes
ORDER BY ABS(CAST(lpa.ZIPCODE AS INT) - 40229) ASC;
