
/*
WHSWorkUser.Worker == WHSWorker.RecId
WHSWorker.Worker == HcmWorker.RecId

DirPerson.RecId == HcmWorker.Person -- DirPerson is not visible to query from?

HCmWorker.Person == DirPersonName.Person

HcmWorker.Worker == DirPersonUser.PersonParty

HcmWorker.Person == DirAddressBookPartyAllView.Party
DirPartyTable
DirPersonName
HcmPersonDetails
HcmVeteranStatus
DirNameSequence.RecID == DirPerson.NameSequence
*/

--sp_columns HcmWorker

SELECT 
    wkusr.USERID
    , wkusr.USERNAME
    , CASE WHEN wkusr.[DISABLED] = 0 THEN 'Active' ELSE 'Inactive' END AS 'UserStatus'
    --, hcmwkr. PERSONNELNUMBER   AS UnderPersonnelNumber
    , CONCAT( rtrim(dpname.FIRSTNAME), ' ', ltrim(dpname.MIDDLENAME), ' ', dpname.LASTNAME) AS UnderName
   -- , wkusr.USERDEFAULTWAREHOUSE
    --, wkusr.MENUNAME
FROM    
    WHSWorkUser wkusr
    LEFT JOIN WHSWORKER wkr ON wkr.RECID = wkusr.WORKER AND wkr.DATAAREAID = 'ha' AND wkr.[PARTITION] = '5637144576'
    LEFT JOIN HCMWORKER hcmwkr ON hcmwkr.RECID = wkr.WORKER AND wkr.DATAAREAID = 'ha' AND wkr.[PARTITION] = '5637144576'
    LEFT JOIN DIRPERSONNAME dpname ON dpname.PERSON = hcmwkr.PERSON AND dpname.[PARTITION] = '5637144576'
WHERE
    --wkusr.USERID IN ( 'yacr' )
    wkusr.USERNAME LIKE 'Idelmis%'